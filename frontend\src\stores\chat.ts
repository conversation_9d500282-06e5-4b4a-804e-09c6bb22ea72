import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { streamChatMessage } from '@/api/chat'

export interface Message {
    role: 'user' | 'assistant'
    content: string
    timestamp: string
}

export interface Conversation {
    id: number
    title: string
    created_at: string
}

export const useChatStore = defineStore('chat', () => {
    const conversations = ref<Conversation[]>([])
    const currentConversationId = ref<number | null>(null)
    const messages = ref<Record<number, Message[]>>({})
    const isLoading = ref(false)
    const isSidebarCollapsed = ref(false)

    const currentConversation = computed(() =>
        conversations.value.find(c => c.id === currentConversationId.value)
    )

    const currentMessages = computed(() =>
        currentConversationId.value ? messages.value[currentConversationId.value] || [] : []
    )

    const loadConversations = async () => {
        isLoading.value = true
        try {
            // TODO: 调用后端API获取对话列表
            // const response = await chatApi.getConversations()
            // conversations.value = response

            // 临时模拟数据
            conversations.value = [
                {
                    id: 1,
                    title: '新的对话',
                    created_at: new Date().toISOString()
                }
            ]
        } catch (error) {
            console.error('加载对话列表失败:', error)
        } finally {
            isLoading.value = false
        }
    }

    const createConversation = async (userId: number) => {
        try {
            // TODO: 调用后端API创建新对话
            // const response = await chatApi.createConversation({ user_id: userId })

            // 临时模拟创建对话
            const newConversation: Conversation = {
                id: Date.now(),
                title: '新的对话',
                created_at: new Date().toISOString()
            }

            conversations.value.unshift(newConversation)
            currentConversationId.value = newConversation.id
            messages.value[newConversation.id] = []

            return newConversation
        } catch (error) {
            console.error('创建对话失败:', error)
            return null
        }
    }

    const loadMessages = async (conversationId: number) => {
        try {
            // TODO: 调用后端API获取历史消息
            // const response = await chatApi.getMessages(conversationId)
            // messages.value[conversationId] = response[conversationId] || []

            // 临时模拟数据
            if (!messages.value[conversationId]) {
                messages.value[conversationId] = []
            }
        } catch (error) {
            console.error('加载消息失败:', error)
        }
    }

    const sendMessage = async (conversationId: number, content: string) => {
        // 添加用户消息
        const userMessage: Message = {
            role: 'user',
            content,
            timestamp: new Date().toISOString()
        }

        if (!messages.value[conversationId]) {
            messages.value[conversationId] = []
        }

        messages.value[conversationId].push(userMessage)

        let assistantMessage: Message = {
            role: 'assistant',
            content: '',
            timestamp: new Date().toISOString()
        }
        messages.value[conversationId].push(assistantMessage)

        try {
            streamChatMessage({
                conversationId,
                message: content,
                onMessage: (data: string) => {
                    assistantMessage.content += data
                },
                onError: (err) => {
                    assistantMessage.content += '\n[发生错误，AI回复中断]'
                    console.error('发送消息失败:', err)
                },
                onComplete: () => {
                    assistantMessage.timestamp = new Date().toISOString()
                }
            })
        } catch (error) {
            assistantMessage.content += '\n[发生错误，AI回复中断]'
            console.error('发送消息失败:', error)
        }
    }

    const selectConversation = (conversationId: number) => {
        currentConversationId.value = conversationId
        loadMessages(conversationId)
    }

    const toggleSidebar = () => {
        isSidebarCollapsed.value = !isSidebarCollapsed.value
    }

    return {
        conversations,
        currentConversationId,
        messages,
        isLoading,
        isSidebarCollapsed,
        currentConversation,
        currentMessages,
        loadConversations,
        createConversation,
        loadMessages,
        sendMessage,
        selectConversation,
        toggleSidebar
    }
}) 