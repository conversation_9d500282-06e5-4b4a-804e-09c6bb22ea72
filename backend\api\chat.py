"""Chat API routes with SSE streaming."""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
import logging

from crud.database import get_database_session
from schemas.message import ChatRequest
from services.message_service import MessageService
from utils.dependencies import get_current_username

router = APIRouter(prefix="/api", tags=["聊天对话"])
message_service = MessageService()
logger = logging.getLogger(__name__)


@router.post("/chat/stream", summary="发送消息（SSE流返回）")
async def chat_stream(
    chat_data: ChatRequest,
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session),
):
    """发送消息并以SSE流的形式返回AI回复"""
    logger.info(
        f"[chat_stream] 用户: {current_username}, 对话ID: {chat_data.conversation_id}, 消息: {chat_data.message}"
    )

    async def generate_response():
        """生成SSE格式的流式响应"""
        try:
            # 1. 验证用户权限并保存用户消息
            user_message = message_service.create_user_message(
                db, chat_data.conversation_id, chat_data.message, current_username
            )
            logger.info(f"[chat_stream] 用户消息已保存: {user_message}")

            # 2. 生成AI回复并流式返回
            ai_response_content = ""
            for chunk in message_service.generate_chat_response(chat_data):
                ai_response_content += chunk
                logger.debug(f"[chat_stream] AI回复流片段: {chunk}")
                yield f'data: {{"content": "{chunk}"}}\n\n'

            # 3. 保存AI回复到数据库
            message_service.create_assistant_message(
                db, chat_data.conversation_id, ai_response_content
            )
            logger.info(f"[chat_stream] AI回复已保存, 长度: {len(ai_response_content)}")

            yield "data: [DONE]\n\n"

        except ValueError as e:
            logger.warning(f"[chat_stream] 参数错误: {e}")
            yield f'data: {{"error": "{str(e)}"}}\n\n'
        except Exception as e:
            logger.error(f"[chat_stream] 处理消息时发生异常: {e}", exc_info=True)
            yield f'data: {{"error": "处理消息时发生错误: {str(e)}"}}\n\n'

    return StreamingResponse(
        generate_response(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Credentials": "true",
        },
    )
