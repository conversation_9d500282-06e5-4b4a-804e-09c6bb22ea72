// 聊天相关 API 封装
export function streamChatMessage({ conversationId, message, onMessage, onError, onComplete }: {
    conversationId: number,
    message: string,
    onMessage: (data: string) => void,
    onError?: (err: any) => void,
    onComplete?: () => void
}) {
    const url = `/api/chat/stream`;
    const eventSource = new EventSource(url + `?conversation_id=${conversationId}&message=${encodeURIComponent(message)}`);

    eventSource.onmessage = (event) => {
        onMessage(event.data);
    };

    eventSource.onerror = (err) => {
        if (onError) onError(err);
        eventSource.close();
    };

    eventSource.onopen = () => {
        // 连接建立
    };

    eventSource.addEventListener('end', () => {
        if (onComplete) onComplete();
        eventSource.close();
    });

    return eventSource;
} 