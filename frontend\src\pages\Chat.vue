<template>
  <div class="flex h-screen bg-white">
    <!-- 侧边栏 -->
    <aside 
      :class="[
        'bg-gray-900 text-white transition-all duration-300 ease-in-out flex flex-col',
        isSidebarCollapsed ? 'w-16' : 'w-80'
      ]"
    >
      <!-- 头部 -->
      <div class="p-4 border-b border-gray-700">
        <div class="flex items-center justify-between">
          <div v-if="!isSidebarCollapsed" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <MessageCircle :size="20" />
            </div>
            <span class="font-semibold">聊天系统</span>
          </div>
          
          <button
            @click="toggleSidebar"
            class="p-2 hover:bg-gray-700 rounded-lg transition-colors"
          >
            <Menu :size="20" />
          </button>
        </div>
      </div>

      <!-- 新建对话按钮 -->
      <div class="p-4">
        <button
          @click="createNewConversation"
          :class="[
            'w-full bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors flex items-center justify-center space-x-2',
            isSidebarCollapsed ? 'p-3' : 'p-3'
          ]"
        >
          <Plus :size="20" />
          <span v-if="!isSidebarCollapsed">新建对话</span>
        </button>
      </div>

      <!-- 对话列表 -->
      <div class="flex-1 overflow-y-auto px-4 pb-4">
        <div v-if="conversations.length === 0" class="text-gray-400 text-center py-8">
          <span v-if="!isSidebarCollapsed">暂无对话</span>
        </div>
        
        <div v-else class="space-y-2">
          <div
            v-for="conversation in conversations"
            :key="conversation.id"
            @click="selectConversation(conversation.id)"
            :class="[
              'p-3 rounded-lg cursor-pointer transition-colors',
              currentConversationId === conversation.id 
                ? 'bg-primary-600' 
                : 'hover:bg-gray-700'
            ]"
          >
            <div v-if="!isSidebarCollapsed" class="truncate">
              {{ conversation.title }}
            </div>
            <div v-else class="flex justify-center">
              <MessageSquare :size="20" />
            </div>
          </div>
        </div>
      </div>

      <!-- 用户信息 -->
      <div class="p-4 border-t border-gray-700">
        <div class="flex items-center justify-between">
          <div v-if="!isSidebarCollapsed" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
              <User :size="16" />
            </div>
            <span class="text-sm">{{ user?.username }}</span>
          </div>
          
          <button
            @click="handleLogout"
            class="p-2 hover:bg-gray-700 rounded-lg transition-colors"
          >
            <LogOut :size="20" />
          </button>
        </div>
      </div>
    </aside>

    <!-- 主聊天区域 -->
    <main class="flex-1 flex flex-col">
      <!-- 聊天头部 -->
      <header class="bg-white border-b border-gray-200 p-4">
        <div class="flex items-center justify-between">
          <h1 class="text-lg font-semibold text-gray-900">
            {{ currentConversation?.title || '选择一个对话开始聊天' }}
          </h1>
        </div>
      </header>

      <!-- 消息列表 -->
      <div class="flex-1 overflow-y-auto p-4 space-y-4" ref="chatContainer">
        <div v-if="currentMessages.length === 0" class="text-center text-gray-500 mt-20">
          <MessageCircle :size="48" class="mx-auto mb-4 text-gray-300" />
          <p>开始新的对话吧！</p>
        </div>
        
        <div
          v-for="(message, index) in currentMessages"
          :key="index"
          :class="[
            'flex',
            message.role === 'user' ? 'justify-end' : 'justify-start'
          ]"
        >
          <div
            :class="[
              'max-w-xs lg:max-w-md px-4 py-2 rounded-lg',
              message.role === 'user'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-900'
            ]"
          >
            <p class="text-sm">{{ message.content }}</p>
            <span class="text-xs opacity-70 mt-1 block">
              {{ formatTime(message.timestamp) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="border-t border-gray-200 p-4">
        <div class="flex space-x-3">
          <input
            v-model="newMessage"
            @keyup.enter="sendMessage"
            :disabled="!currentConversationId || isSending"
            type="text"
            placeholder="输入消息..."
            class="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          />
          <button
            @click="sendMessage"
            :disabled="!newMessage.trim() || !currentConversationId || isSending"
            class="bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-1"
          >
            <Send :size="16" />
            <span>发送</span>
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { 
  MessageCircle, 
  Menu, 
  Plus, 
  MessageSquare, 
  User, 
  LogOut, 
  Send 
} from 'lucide-vue-next'
import { useAuthStore } from '@/stores/auth'
import { useChatStore } from '@/stores/chat'

const router = useRouter()
const authStore = useAuthStore()
const chatStore = useChatStore()

const newMessage = ref('')
const isSending = ref(false)
const chatContainer = ref<HTMLElement | null>(null)

// Computed properties
const user = computed(() => authStore.user)
const conversations = computed(() => chatStore.conversations)
const currentConversationId = computed(() => chatStore.currentConversationId)
const currentConversation = computed(() => chatStore.currentConversation)
const currentMessages = computed(() => chatStore.currentMessages)
const isSidebarCollapsed = computed(() => chatStore.isSidebarCollapsed)

// Methods
const toggleSidebar = () => {
  chatStore.toggleSidebar()
}

const createNewConversation = async () => {
  if (user.value) {
    await chatStore.createConversation(user.value.id)
  }
}

const selectConversation = (conversationId: number) => {
  chatStore.selectConversation(conversationId)
}

const sendMessage = async () => {
  if (!newMessage.value.trim() || !currentConversationId.value) return
  isSending.value = true
  await chatStore.sendMessage(currentConversationId.value, newMessage.value)
  newMessage.value = ''
  await nextTick()
  scrollToBottom()
  isSending.value = false
}

function scrollToBottom() {
  nextTick(() => {
    if (chatContainer.value) {
      chatContainer.value.scrollTop = chatContainer.value.scrollHeight
    }
  })
}

const handleLogout = () => {
  authStore.logout()
  router.push('/login')
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Initialize
onMounted(() => {
  authStore.initializeAuth()
  chatStore.loadConversations()
})
</script> 